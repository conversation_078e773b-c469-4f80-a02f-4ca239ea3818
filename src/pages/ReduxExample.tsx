import { useIsMobile } from '@/hooks/use-mobile';
import Sidebar from '@/components/Sidebar';
import DashboardHeader from '@/components/DashboardHeader';
import OrdersExample from '@/components/redux-examples/OrdersExample';
import UserExample from '@/components/redux-examples/UserExample';

export default function ReduxExample() {
  const isMobile = useIsMobile();

  return (
    <div className="min-h-screen flex bg-gray-50">
      <Sidebar isMobile={isMobile} />
      
      <div className={`flex-1 ${isMobile ? '' : 'ml-64'}`}>
        <DashboardHeader />
        
        <main className="p-6">
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900">Redux Examples</h1>
            <p className="text-gray-600">Examples of Redux integration in the application.</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <OrdersExample />
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow">
              <UserExample />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
