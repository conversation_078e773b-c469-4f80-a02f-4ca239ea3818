
import React from 'react';
import { format } from 'date-fns';

interface WeekViewCalendarEventProps {
  event: {
    id: string;
    title: string;
    start: number;
    end: number;
    inspector: string;
    color: string;
  };
  onDragStart: (e: React.DragEvent, eventId: string) => void;
  position?: {
    width: number;
    left: number;
    zIndex?: number;
  };
}

const WeekViewCalendarEvent: React.FC<WeekViewCalendarEventProps> = ({
  event,
  onDragStart,
  position = { width: 100, left: 0 }
}) => {
  const startDate = new Date(event.start);
  const endDate = new Date(event.end);
  const height = (endDate.getHours() - startDate.getHours()) * 60;

  return (
    <div
      className={`absolute top-0 p-1.5 text-xs text-white rounded-md cursor-grab shadow-sm hover:shadow-md transition-all ${event.color} hover:brightness-110`}
      style={{
        height: `${height}px`,
        width: `${position.width}%`,
        left: `${position.left}%`,
        zIndex: position.zIndex || 1,
        transition: 'width 0.2s, left 0.2s, box-shadow 0.2s, transform 0.1s',
        borderLeft: '3px solid rgba(255, 255, 255, 0.5)',
        transform: 'translateZ(0)'
      }}
      title={`${format(event.start, 'HH:mm')} - ${format(event.end, 'HH:mm')}: ${event.title}`}
      draggable
      onDragStart={e => onDragStart(e, event.id)}
    >
      <div className="truncate font-semibold text-sm">{event.title}</div>
      <div className="text-xs font-medium truncate mt-0.5">{event.inspector}</div>
      <div className="text-xs bg-white/20 rounded px-1 py-0.5 mt-1 inline-block">{format(event.start, 'HH:mm')} - {format(event.end, 'HH:mm')}</div>
    </div>
  );
};

export default WeekViewCalendarEvent;

