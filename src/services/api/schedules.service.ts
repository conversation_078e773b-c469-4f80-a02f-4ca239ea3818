import { apiClient } from '@/lib/api-client';
import { Schedule } from '../../types/schedule.type';

export const scheduleService = {
  getSchedules: async (page: number = 1, pageSize: number = 10) => { 
    return await apiClient<Schedule[]>(`/api/schedules?page=${page}&pageSize=${pageSize}`);
  },

  getScheduleById: async (id: string) => {
    return await apiClient<Schedule>(`/api/schedules/${id}`);
  },

  createSchedule: async (scheduleData: Omit<Schedule, 'id' | 'createdAt' | 'updatedAt' | 'inspectorName'>) => {
    return await apiClient<{ success: boolean; data: Schedule }>('/api/schedules', {
      method: 'POST',
      body: JSON.stringify(scheduleData),
    });
  },
};