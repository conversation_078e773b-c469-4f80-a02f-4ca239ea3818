import React, { useState } from "react";
import WeekViewCalendarEventV2 from "./WeekViewCalendarEventV2";
import { getWeekViewDragEventData } from "./weekViewUtils";

interface CalendarEvent {
  id: string;
  title: string;
  start: number;
  end: number;
  inspector: string;
  color: string;
}

interface EventPosition {
  width: number;
  left: number;
  zIndex?: number;
}

function calculateEventPositions(
  events: CalendarEvent[]
): (CalendarEvent & { position: EventPosition })[] {
  if (events.length <= 1) {
    return events.map((event) => ({
      ...event,
      position: { width: 92, left: 4 },
    }));
  }

  const eventsByInspector: Record<string, CalendarEvent[]> = {};
  events.forEach((event) => {
    if (!eventsByInspector[event.inspector]) {
      eventsByInspector[event.inspector] = [];
    }
    eventsByInspector[event.inspector].push(event);
  });

  const inspectors = Object.keys(eventsByInspector);
  const inspectorCount = inspectors.length;

  const inspectorWidth = Math.max(90 / inspectorCount, 25);
  const result: (CalendarEvent & { position: EventPosition })[] = [];

  inspectors.forEach((inspector, index) => {
    const inspectorEvents = eventsByInspector[inspector];
    const inspectorLeft = 5 + index * inspectorWidth;

    inspectorEvents.forEach((event) => {
      result.push({
        ...event,
        position: {
          width: inspectorWidth - 2,
          left: inspectorLeft,
          zIndex: index + 1,
        },
      });
    });
  });

  return result;
}

interface WeekViewCalendarGridV2Props {
  days: Date[];
  hours: number[];
  events: CalendarEvent[];
  handleDrop: (e: React.DragEvent, day: Date, hour: number) => void;
  handleDragStart: (e: React.DragEvent, eventId: string) => void;
}

const WeekViewCalendarGridV2: React.FC<WeekViewCalendarGridV2Props> = ({
  days,
  hours,
  events,
  handleDrop,
  handleDragStart,
}) => {
  const [dragOverCell, setDragOverCell] = useState<string | null>(null);
  const [draggedInspector, setDraggedInspector] = useState<string | null>(null);

  const hasInspectorEvent = (
    day: Date,
    hour: number,
    inspectorName: string
  ) => {
    if (!inspectorName) return false;

    const slotStart = new Date(day);
    slotStart.setHours(hour, 0, 0, 0);
    const slotEnd = new Date(day);
    slotEnd.setHours(hour + 1, 0, 0, 0);

    return events.some(
      (event) =>
        event.inspector === inspectorName &&
        ((event.start >= slotStart.getTime() &&
          event.start < slotEnd.getTime()) ||
          (event.end > slotStart.getTime() && event.end <= slotEnd.getTime()) ||
          (event.start <= slotStart.getTime() &&
            event.end >= slotEnd.getTime()))
    );
  };

  const currentHour = new Date().getHours();
  const currentDay = new Date().getDate();
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();

  return (
    <>
      {hours.map((hour) => (
        <div key={hour} className="grid grid-cols-[100px_repeat(7,1fr)] group">
          <div className="p-2 text-xs font-medium text-gray-500 flex items-center justify-center border-r border-gray-100 bg-gray-50/50">
            <span
              className={`${
                hour === currentHour ? "text-blue-600 font-medium" : ""
              }`}
            >
              {`${hour === 12 ? 12 : hour % 12 || 12}${
                hour < 12 ? "am" : "pm"
              }`}
            </span>
          </div>
          {days.map((day) => {
            const slotStart = new Date(day);
            slotStart.setHours(hour, 0, 0, 0);
            const slotEnd = new Date(day);
            slotEnd.setHours(hour + 1, 0, 0, 0);
            const cellId = `${day.toISOString()}-${hour}`;
            const isBeingDraggedOver = dragOverCell === cellId;

            const isToday =
              day.getDate() === currentDay &&
              day.getMonth() === currentMonth &&
              day.getFullYear() === currentYear;

            const hasConflict =
              draggedInspector &&
              hasInspectorEvent(day, hour, draggedInspector);

            const slotEvents = events.filter(
              (event) =>
                event.start >= slotStart.getTime() &&
                event.start < slotEnd.getTime()
            );

            const eventsWithPositions = calculateEventPositions(slotEvents);

            let cellClass =
              "border-r border-b border-gray-100 p-1.5 min-h-[70px] relative";

            cellClass += " group-hover:bg-gray-50/40";

            if (hour === currentHour && isToday) {
              cellClass += " bg-blue-50/40";
            }

            if (isBeingDraggedOver) {
              cellClass += hasConflict ? " bg-red-50/80" : " bg-emerald-50/80";
            }

            const dayOfWeek = day.getDay();
            if (dayOfWeek === 0 || dayOfWeek === 6) {
              cellClass += " bg-gray-50/30";
            }

            return (
              <div
                key={cellId}
                className={cellClass}
                onDragOver={(e) => {
                  e.preventDefault();
                  setDragOverCell(cellId);

                  try {
                    const data = getWeekViewDragEventData(e);
                    if (data && data.type === "week") {
                      const draggedEvent = events.find(
                        (ev) => ev.id === data.eventId
                      );
                      if (draggedEvent) {
                        setDraggedInspector(draggedEvent.inspector);
                      }
                    }
                  } catch (err) {
                    console.error("Error getting drag data:", err);
                  }
                }}
                onDragLeave={() => {
                  if (dragOverCell === cellId) {
                    setDragOverCell(null);
                  }
                }}
                onDrop={(e) => {
                  setDragOverCell(null);
                  setDraggedInspector(null);
                  handleDrop(e, new Date(day), hour);
                }}
                style={{ minHeight: 60 }}
              >
                {hour === currentHour && isToday && (
                  <div
                    className="absolute left-0 right-0 border-t-2 border-blue-500 z-10"
                    style={{ top: "50%" }}
                  >
                    <div className="absolute -left-1 -top-1.5 w-3 h-3 rounded-full bg-blue-500"></div>
                  </div>
                )}

                {eventsWithPositions.map((event) => (
                  <WeekViewCalendarEventV2
                    key={event.id}
                    event={event}
                    position={event.position}
                    onDragStart={(e, eventId) => {
                      handleDragStart(e, eventId);
                      const draggedEvent = events.find(
                        (ev) => ev.id === eventId
                      );
                      if (draggedEvent) {
                        setDraggedInspector(draggedEvent.inspector);
                      }
                    }}
                  />
                ))}
              </div>
            );
          })}
        </div>
      ))}
    </>
  );
};

export default WeekViewCalendarGridV2;
