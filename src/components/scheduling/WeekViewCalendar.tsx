import React, { useState, useRef, useEffect } from 'react';
import { addDays, startOfWeek, format } from 'date-fns';
import WeekViewCalendarHeader from './WeekViewCalendarHeader';
import WeekViewCalendarGrid from './WeekViewCalendarGrid';
import { getWeekViewDragEventData, generateWeekViewEvents } from './weekViewUtils';
import { useToast } from '@/components/ui/use-toast';
import InspectorFilter from './InspectorFilter';
import { dummyInspectors } from './calendarUtils';

const HOURS = Array.from({ length: 12 }, (_, i) => i + 8);

function eventsOverlap(event1Start: number, event1End: number, event2Start: number, event2End: number) {
  return (
    (event1Start >= event2Start && event1Start < event2End) ||
    (event1End > event2Start && event1End <= event2End) ||
    (event1Start <= event2Start && event1End >= event2End)
  );
}

function checkForOverlap(
  eventId: string,
  inspectorName: string,
  proposedStart: number,
  proposedEnd: number,
  allEvents: Array<{ id: string; start: number; end: number; inspector: string }>
) {
  return allEvents.some(
    existingEvent =>
      existingEvent.id !== eventId &&
      existingEvent.inspector === inspectorName &&
      eventsOverlap(proposedStart, proposedEnd, existingEvent.start, existingEvent.end)
  );
}

const WeekViewCalendar = () => {
  const [allEvents, setAllEvents] = useState(generateWeekViewEvents());
  const [filteredEvents, setFilteredEvents] = useState(allEvents);
  const [selectedInspectorIds, setSelectedInspectorIds] = useState<number[]>(
    dummyInspectors.map(inspector => inspector.id)
  );

  const days = Array.from({ length: 7 }, (_, i) => addDays(startOfWeek(new Date()), i));
  const toastShownRef = useRef(false);
  const { toast } = useToast();

  useEffect(() => {
    if (selectedInspectorIds.length === 0) {
      setFilteredEvents([]);
    } else if (selectedInspectorIds.length === dummyInspectors.length) {
      setFilteredEvents(allEvents);
    } else {
      const selectedInspectorNames = dummyInspectors
        .filter(inspector => selectedInspectorIds.includes(inspector.id))
        .map(inspector => inspector.name);

      setFilteredEvents(
        allEvents.filter(event => selectedInspectorNames.includes(event.inspector))
      );
    }
  }, [selectedInspectorIds, allEvents]);

  const handleInspectorFilterChange = (selectedIds: number[]) => {
    setSelectedInspectorIds(selectedIds);
  };

  function handleDragStart(e: React.DragEvent, eventId: string) {
    e.dataTransfer.setData(
      'application/json',
      JSON.stringify({
        eventId,
        type: 'week',
      })
    );
    e.dataTransfer.effectAllowed = 'move';
    toastShownRef.current = false;
  }

  function handleDrop(e: React.DragEvent, day: Date, hour: number) {
    const data = getWeekViewDragEventData(e);
    if (!data || data.type !== 'week') return;

    setAllEvents(prev => {
      const idx = prev.findIndex(ev => ev.id === data.eventId);
      if (idx === -1) return prev;

      const event = { ...prev[idx] };
      const origDuration = new Date(event.end).getHours() - new Date(event.start).getHours();

      const start = new Date(day);
      start.setHours(hour, 0, 0, 0);
      const end = new Date(day);
      end.setHours(hour + origDuration, 0, 0, 0);

      const proposedStart = start.getTime();
      const proposedEnd = end.getTime();

      const hasOverlap = checkForOverlap(event.id, event.inspector, proposedStart, proposedEnd, prev);

      if (hasOverlap) {
        if (!toastShownRef.current) {
          toast({
            title: "Cannot schedule here",
            description: `Inspector ${event.inspector} already has an event scheduled during this time.`,
            variant: "destructive"
          });
          toastShownRef.current = true;
        }
        return prev;
      }

      event.start = proposedStart;
      event.end = proposedEnd;
      const updated = [...prev];
      updated[idx] = event;
      return updated;
    });
  }

  return (
    <div className="h-[600px] border border-gray-200 rounded-xl shadow-sm overflow-hidden flex flex-col bg-white/95 backdrop-blur-sm">
      {/* <div className="px-4 py-3 border-b bg-gray-50/30">
        <InspectorFilter
          inspectors={dummyInspectors}
          selectedInspectors={selectedInspectorIds}
          onSelectionChange={handleInspectorFilterChange}
        />
      </div> */}

      <div className="flex-1 overflow-auto p-0 scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent">
        <div className="min-w-[800px]">
          <WeekViewCalendarGrid
            days={days}
            hours={HOURS}
            events={filteredEvents}
            handleDrop={handleDrop}
            handleDragStart={handleDragStart}
          />
        </div>
      </div>
    </div>
  );
};

export default WeekViewCalendar;
