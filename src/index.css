@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-size: 15px; /* Decreased from default 16px */
  }

  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 210 40% 98%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 213 68% 24%;
    --primary-foreground: 210 40% 98%;

    --secondary: 182 59% 33%;
    --secondary-foreground: 210 40% 98%;

    --muted: 213 27% 84%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 47 84% 61%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 213 68% 24%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 213 68% 24%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 213 68% 24%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 213 68% 40%;
    --primary-foreground: 210 40% 98%;

    --secondary: 182 59% 33%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 47 84% 61%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 213 68% 40%;
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 213 68% 40%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 213 68% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
}

@layer utilities {
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-realestate-700 to-realestate-500 bg-clip-text text-transparent;
  }
  
  .property-card {
    @apply bg-white rounded-lg shadow overflow-hidden card-hover border border-gray-100;
  }
}

/* Custom styles for our real estate app */
.hero-section {
  background-image: linear-gradient(rgba(26, 54, 93, 0.85), rgba(26, 54, 93, 0.95)), url('/src/assets/hero-bg.jpg');
  background-size: cover;
  background-position: center;
}

@import './styles/shake-animation.css';

/* Custom, rounded and slim sidebar scrollbar styling */
.styled-scrollbar {
  scrollbar-color: #e5e7eb #ffffff;
  scrollbar-width: 12px;
}

/* Chrome/Edge/Safari and most Webkit */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 6px;
  min-height: 24px;
  min-width: 24px;
}

::-webkit-scrollbar-corner {
  background: #f9fafb;
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #e5e7eb #f9fafb;
}

.styled-scrollbar::-webkit-scrollbar {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background: #f9fafb;
}
.styled-scrollbar::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 6px;
}

/* Ensure all scrollable tabs and content get the bigger scrollbar */
[data-radix-scroll-area-viewport],
[data-radix-tabs-content],
.scroll-area,
.tabs-content {
  scrollbar-width: 12px;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar,
[data-radix-tabs-content]::-webkit-scrollbar,
.scroll-area::-webkit-scrollbar,
.tabs-content::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb,
[data-radix-tabs-content]::-webkit-scrollbar-thumb,
.scroll-area::-webkit-scrollbar-thumb,
.tabs-content::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 6px;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-corner,
[data-radix-tabs-content]::-webkit-scrollbar-corner,
.scroll-area::-webkit-scrollbar-corner,
.tabs-content::-webkit-scrollbar-corner {
  background: #f9fafb;
}

/* For quick action black tooltip */
.tooltip-black .radix-tooltip-content,
.tooltip-black [data-radix-popper-content-wrapper] {
  background: #000 !important;
  color: #fff !important;
  border-radius: 6px !important;
  padding: 0.35em 0.66em !important;
  font-size: 0.93em !important;
  z-index: 99;
}
