import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User, UserState } from '@/types/user';
import { apiClient, AuthResponse } from '@/lib/api-client';

const initialState: UserState = {
  currentUser: null,
  loading: false,
  error: null,
};

// Async thunks
export const fetchCurrentUser = createAsyncThunk(
  'user/fetchCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      // Check if we have a user in localStorage first
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          // If we have a stored user, use that first for immediate UI update
          const parsedUser = JSON.parse(storedUser);

          // Then fetch the latest user data from the API
          const response = await apiClient<{ user: User }>('auth/me');

          // If the API call succeeds, return the latest user data
          if (response) {
            return response.user;
          }

          // If the API call fails but we have stored user data, use that
          return parsedUser;
        } catch (parseError: unknown) {
          // If parsing fails, continue with the API call
          console.error('Failed to parse stored user:', parseError);
        }
      }

      // If no stored user or parsing failed, make the API call
      const response = await apiClient<{ user: User }>('auth/me');

      if (!response) {
        return rejectWithValue('Failed to fetch user');
      }

      return response.user;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch user');
    }
  }
);

export const loginUser = createAsyncThunk(
  'user/loginUser',
  async ({ email, password }: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await apiClient<AuthResponse>('auth/login', {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      });

      if (!response) {
        return rejectWithValue('Failed to login');
      }

      // Store tokens in localStorage
      localStorage.setItem('access_token', response.accessToken);
      localStorage.setItem('refresh_token', response.refreshToken);
      localStorage.setItem('user', JSON.stringify(response.user));

      return response.user;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to login');
    }
  }
);

export const registerUser = createAsyncThunk(
  'user/registerUser',
  async ({ email, password, name }: { email: string; password: string; name: string }, { rejectWithValue }) => {
    try {
      const response = await apiClient<AuthResponse>('auth/register', {
        method: 'POST',
        body: JSON.stringify({ email, password, name }),
      });

      if (!response) {
        return rejectWithValue('Failed to register');
      }

      // Store tokens in localStorage
      localStorage.setItem('access_token', response.accessToken);
      localStorage.setItem('refresh_token', response.refreshToken);
      localStorage.setItem('user', JSON.stringify(response.user));

      return response.user;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to register');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateUserProfile',
  async (userData: Partial<User>, { rejectWithValue }) => {
    try {
      // Update user profile
      const response = await apiClient<{ user: User }>('auth/me', {
        method: 'PATCH',
        body: JSON.stringify(userData),
      });

      if (!response) {
        return rejectWithValue('Failed to update profile');
      }

      // Update user in localStorage
      const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
      localStorage.setItem('user', JSON.stringify({ ...storedUser, ...response.user }));

      return response.user;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update profile');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'user/logoutUser',
  async (_, { rejectWithValue }) => {
    try {
      // Clear localStorage
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');

      return null;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to logout');
    }
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
    },
    clearUserError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch current user
      .addCase(fetchCurrentUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCurrentUser.fulfilled, (state, action) => {
        state.loading = false;
        state.currentUser = action.payload;
      })
      .addCase(fetchCurrentUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Login user
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.currentUser = action.payload;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Register user
      .addCase(registerUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.loading = false;
        state.currentUser = action.payload;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update user profile
      .addCase(updateUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.currentUser = { ...state.currentUser, ...action.payload };
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Logout user
      .addCase(logoutUser.fulfilled, (state) => {
        state.currentUser = null;
      });
  },
});

export const { setUser, clearUserError } = userSlice.actions;
export default userSlice.reducer;
