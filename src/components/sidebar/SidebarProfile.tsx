
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { useAppSelector } from '@/redux/hooks';

export function SidebarProfile({ isOpen }: { isOpen: boolean }) {
  // Get current user from Redux store
  const { currentUser } = useAppSelector(state => state.user);

  // Get first letter of name for avatar fallback
  const getInitial = (name: string) => {
    return name ? name.charAt(0).toUpperCase() : 'U';
  };

  return (
    <div className={cn(
      "flex items-center p-4 border-t border-gray-200",
      isOpen ? "justify-between" : "justify-center"
    )}>
      <div className="flex items-center">
        <Avatar className="h-8 w-8">
          {currentUser?.avatar ? (
            <AvatarImage src={currentUser.avatar} alt={currentUser.name} />
          ) : (
            <AvatarImage src="/placeholder.svg" alt="User" />
          )}
          <AvatarFallback>{currentUser ? getInitial(currentUser.name) : 'U'}</AvatarFallback>
        </Avatar>
        {isOpen && (
          <div className="ml-3">
            <p className="text-sm font-medium">{currentUser?.name || 'User Name'}</p>
            <p className="text-xs text-gray-500">{currentUser?.email || '<EMAIL>'}</p>
          </div>
        )}
      </div>
    </div>
  );
}
