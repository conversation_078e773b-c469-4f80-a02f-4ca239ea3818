export interface Schedule {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  available: boolean;
  inspectorId: number;
  inspectorName: string;
  inspectionOrderId: number;
  createdAt: string;
  updatedAt: string;
}

export interface ScheduleResponse {
  items: Schedule[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}
