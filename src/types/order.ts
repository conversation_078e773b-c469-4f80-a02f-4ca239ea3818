export type StatusType =
  | "pending"
  | "cancelled"
  | "scheduled"
  | "in progress"
  | "inspected"
  | "reported"
  | "completed";

export type PropertyType = "single" | "multi" | "commercial" | "land" | "other";

export interface Order {
  id: number;
  inspectionOrderId: string;
  clientName: string;
  clientPhone: string;
  clientEmail: string;
  propertyAddress: string;
  propertyType: PropertyType;
  status: StatusType;
  inspectionDate: string;
  inspectionFees: number;
  assignedInspectorIds: number[];
  createdAt: string;
  updatedAt: string;
}

export interface Pagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

export interface OrdersApiResponse {
  orders: Order[];
  pagination: Pagination;
}

export interface OrdersState {
  items: Order[];
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  loading: boolean;
  error: string | null;
}

export interface OrderFormData {
  id: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  status: string;
  propertyType?: string;
  yearBuilt?: string;
  propertyAge?: string;
  foundationType?: string;
  gateCode?: string;
  lockboxCode?: string;
  mlsNumber?: string;
  isClientAttending?: boolean;
  isOccupied?: boolean;
  hasUtilities?: boolean;
  hasAlarm?: boolean;
  services?: {
    flexfund?: boolean;
    mold?: boolean;
  };
  agentName?: string;
  agentEmail?: string;
  agentPhone?: string;
  isSeller?: boolean;
  isBuyer?: boolean;
  inspectionFee?: string;
  thirdPartyFee?: string;
  discountFee?: string;
  processingFee?: string;
}