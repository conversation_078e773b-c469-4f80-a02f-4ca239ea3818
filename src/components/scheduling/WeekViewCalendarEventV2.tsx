import React, { useState } from "react";
import { format } from "date-fns";
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

interface WeekViewCalendarEventV2Props {
  event: {
    id: string;
    title: string;
    start: number;
    end: number;
    inspector: string;
    color: string;
  };
  onDragStart: (e: React.DragEvent, eventId: string) => void;
  position?: {
    width: number;
    left: number;
    zIndex?: number;
  };
}

const WeekViewCalendarEventV2: React.FC<WeekViewCalendarEventV2Props> = ({
  event,
  onDragStart,
  position = { width: 100, left: 0 },
}) => {
  const [showDialog, setShowDialog] = useState(false);
  const navigate = useNavigate();

  const startDate = new Date(event.start);
  const endDate = new Date(event.end);
  const height = (endDate.getHours() - startDate.getHours()) * 60;

  const isShortEvent = height <= 60;

  // Convert Tailwind color classes
  const baseColor = event.color.replace("bg-", "border-");
  const textColor = event.color.replace("bg-", "text-");
  const bgColor = event.color.replace("bg-", "bg-") + "/10";

  // Extract inspection order ID from title (assuming format "Inspection #123")
  const inspectionOrderId = event.title.split("#")[1];

  const handleViewDetails = () => {
    navigate(`/orders/${inspectionOrderId}`);
    setShowDialog(false);
  };

  return (
    <>
      <TooltipProvider delayDuration={300}>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={`absolute top-0 px-2 py-1.5 text-xs rounded-md cursor-pointer transition-all bg-white hover:shadow-md ${baseColor} border-l-3`}
              style={{
                height: `${height - 4}px`,
                width: `${position.width - 2}%`,
                left: `${position.left + 1}%`,
                zIndex: position.zIndex || 1,
                transition: "all 0.2s",
                backgroundColor: "white",
                border: "1px solid rgba(0, 0, 0, 0.08)",
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
              }}
              draggable
              onDragStart={(e) => onDragStart(e, event.id)}
              onClick={() => setShowDialog(true)}
            >
              {isShortEvent ? (
                <div className="h-full flex flex-col justify-between overflow-hidden">
                  <div className="flex items-center gap-1.5">
                    <div
                      className={`w-2.5 h-2.5 rounded-full flex-shrink-0 ${event.color}`}
                      aria-hidden="true"
                    ></div>
                    <div className="truncate font-medium text-gray-900 text-xs">
                      {event.title}
                    </div>
                  </div>
                  <div
                    className={`
                      inline-flex items-center px-1.5 py-0.5 rounded-full ${bgColor}
                      ${textColor} text-xs font-medium truncate max-w-full
                    `}
                  >
                    <div
                      className={`w-1.5 h-1.5 rounded-full mr-1 ${event.color}`}
                      aria-hidden="true"
                    ></div>
                    <span className="truncate text-[10px]">
                      {event.inspector}
                    </span>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex items-start mb-1.5 gap-1.5">
                    <div
                      className={`w-2.5 h-2.5 rounded-full flex-shrink-0 mt-1 ${event.color}`}
                      aria-hidden="true"
                    ></div>
                    <div className="truncate font-medium text-gray-900 text-sm leading-tight">
                      {event.title}
                    </div>
                  </div>

                  <div className="text-xs font-normal text-gray-500 mb-1.5">
                    {format(event.start, "HH:mm")} - {format(event.end, "HH:mm")}
                  </div>

                  <div
                    className={`
                      inline-flex items-center px-2 py-1 rounded-full ${bgColor}
                      ${textColor} text-xs font-medium mt-0.5 max-w-full truncate
                    `}
                  >
                    <div
                      className={`w-2 h-2 rounded-full mr-1 ${event.color}`}
                      aria-hidden="true"
                    ></div>
                    <span className="truncate">{event.inspector}</span>
                  </div>
                </>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            {`${format(event.start, "HH:mm")} - ${format(event.end, "HH:mm")}: ${
              event.title
            } (${event.inspector})`}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl">{event.title}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <div>
                <span className="font-medium text-gray-700">Inspector:</span>{" "}
                <span className="text-gray-900">{event.inspector}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Time:</span>{" "}
                <span className="text-gray-900">
                  {format(event.start, "HH:mm")} - {format(event.end, "HH:mm")}
                </span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Date:</span>{" "}
                <span className="text-gray-900">
                  {format(event.start, "MMMM d, yyyy")}
                </span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              Close
            </Button>
            <Button onClick={handleViewDetails}>View Details</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default WeekViewCalendarEventV2;
