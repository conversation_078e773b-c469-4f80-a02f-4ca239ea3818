import { useToast } from "@/components/ui/use-toast";
import { inspectorsService } from "@/services/api/inspectors.service";
import { Inspector } from "@/types/inspector";
import {
  addDays,
  addWeeks,
  startOfWeek,
  subWeeks
} from "date-fns";
import React, { useEffect, useRef, useState } from "react";
import WeekViewCalendarGridV2 from "./WeekViewCalendarGridV2";
import WeekViewCalendarHeaderV2 from "./WeekViewCalendarHeaderV2";
import { generateWeekViewEvents, getWeekViewDragEventData } from "./weekViewUtils";

const HOURS = Array.from({ length: 12 }, (_, i) => i + 8);
const USE_DUMMY_DATA = false; // Toggle this to switch between dummy data and API data

const COLORS = [
  'bg-indigo-500',
  'bg-emerald-500',
  'bg-violet-500',
  'bg-amber-500',
  'bg-rose-500',
  'bg-cyan-500'
];

const DEFAULT_COLOR = 'bg-blue-500';

interface CalendarEvent {
  id: string;
  title: string;
  inspector: string;
  start: number;
  end: number;
  color: string;
}

function eventsOverlap(
  event1Start: number,
  event1End: number,
  event2Start: number,
  event2End: number
) {
  return (
    (event1Start >= event2Start && event1Start < event2End) ||
    (event1End > event2Start && event1End <= event2End) ||
    (event1Start <= event2Start && event1End >= event2End)
  );
}

function checkForOverlap(
  eventId: string,
  inspectorName: string,
  proposedStart: number,
  proposedEnd: number,
  allEvents: Array<{
    id: string;
    start: number;
    end: number;
    inspector: string;
  }>
) {
  return allEvents.some(
    (existingEvent) =>
      existingEvent.id !== eventId &&
      existingEvent.inspector === inspectorName &&
      eventsOverlap(
        proposedStart,
        proposedEnd,
        existingEvent.start,
        existingEvent.end
      )
  );
}

const WeekViewCalendarV2 = () => {
  const [allEvents, setAllEvents] = useState<CalendarEvent[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<CalendarEvent[]>([]);
  const [inspectors, setInspectors] = useState<Inspector[]>([]);
  const [selectedInspectorIds, setSelectedInspectorIds] = useState<number[]>([]);
  const [isLoadingInspectors, setIsLoadingInspectors] = useState(false);
  const [isLoadingSchedules, setIsLoadingSchedules] = useState(false);
  const [inspectorColors, setInspectorColors] = useState<Record<number, string>>({});
  const [currentWeekStart, setCurrentWeekStart] = useState(
    startOfWeek(new Date())
  );

  const days = Array.from({ length: 7 }, (_, i) =>
    addDays(currentWeekStart, i)
  );
  const toastShownRef = useRef(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchInspectors = async () => {
      setIsLoadingInspectors(true);
      try {
        const response = await inspectorsService.getInspectors();
        if (response && response.inspectors) {
          setInspectors(response.inspectors);
          setSelectedInspectorIds(response.inspectors.map(inspector => inspector.id));
          
          const colorMap: Record<number, string> = {};
          response.inspectors.forEach((inspector, index) => {
            colorMap[inspector.id] = COLORS[index % COLORS.length];
          });
          setInspectorColors(colorMap);
        }
      } catch (err) {
        console.error("Error fetching inspectors:", err);
        // toast({
        //   title: "Error",
        //   description: "Failed to load inspectors. Please try again later.",
        //   variant: "destructive",
        // });
      } finally {
        setIsLoadingInspectors(false);
      }
    };

    fetchInspectors();
  }, [toast]);

  useEffect(() => {
    const fetchSchedules = async () => {
      setIsLoadingSchedules(true);

      // TODO: Replace with scheduleService call when it is ready
      try {
        let schedules;
        
        if (USE_DUMMY_DATA) {
          schedules = generateWeekViewEvents();
          setAllEvents(schedules);
          return;
        } else {
          const response = await fetch(`https://v0-building-restful-api.vercel.app/api/schedules?page=1&pageSize=10`);
          const result = await response.json();
          schedules = result.schedules;
        }

        if (schedules) {
          const events: CalendarEvent[] = schedules.map(schedule => {
            const datePart = schedule.date.split('T')[0];
            const startDateTime = new Date(`${datePart}T${schedule.startTime}`);
            const endDateTime = new Date(`${datePart}T${schedule.endTime}`);
            
            return {
              id: schedule.id,
              title: `Inspection #${schedule.inspectionOrderId}`,
              inspector: schedule.inspectorName,
              start: startDateTime.getTime(),
              end: endDateTime.getTime(),
              color: inspectorColors[schedule.inspectorId] || DEFAULT_COLOR,
            };
          });
          
          setAllEvents(events);
        }
      } catch (err) {
        console.error("Error fetching schedules:", err);
        // toast({
        //   title: "Error",
        //   description: "Failed to load schedules. Please try again later.",
        //   variant: "destructive",
        // });
      } finally {
        setIsLoadingSchedules(false);
      }
    };

    if (inspectors.length > 0) {
      fetchSchedules();
    }
  }, [currentWeekStart, toast, inspectorColors, inspectors]);

  const goToToday = () => {
    setCurrentWeekStart(startOfWeek(new Date()));
  };

  const goToNextWeek = () => {
    setCurrentWeekStart((prevDate) => addWeeks(prevDate, 1));
  };

  const goToPrevWeek = () => {
    setCurrentWeekStart((prevDate) => subWeeks(prevDate, 1));
  };

  useEffect(() => {
    if (selectedInspectorIds.length === 0) {
      setFilteredEvents([]);
    } else if (selectedInspectorIds.length === inspectors.length) {
      setFilteredEvents(allEvents);
    } else {
      const selectedInspectorNames = inspectors
        .filter((inspector) => selectedInspectorIds.includes(inspector.id))
        .map((inspector) => inspector.name);

      setFilteredEvents(
        allEvents.filter((event) =>
          selectedInspectorNames.includes(event.inspector)
        )
      );
    }
  }, [selectedInspectorIds, allEvents, inspectors]);

  const handleInspectorFilterChange = (selectedIds: number[]) => {
    setSelectedInspectorIds(selectedIds);
  };

  function handleDragStart(e: React.DragEvent, eventId: string) {
    e.dataTransfer.setData(
      "application/json",
      JSON.stringify({
        eventId,
        type: "week",
      })
    );
    e.dataTransfer.effectAllowed = "move";
    toastShownRef.current = false;
  }

  function handleDrop(e: React.DragEvent, day: Date, hour: number) {
    const data = getWeekViewDragEventData(e);
    if (!data || data.type !== "week") return;

    setAllEvents((prev) => {
      const idx = prev.findIndex((ev) => ev.id === data.eventId);
      if (idx === -1) return prev;

      const event = { ...prev[idx] };
      const origDuration =
        new Date(event.end).getHours() - new Date(event.start).getHours();

      const start = new Date(day);
      start.setHours(hour, 0, 0, 0);
      const end = new Date(day);
      end.setHours(hour + origDuration, 0, 0, 0);

      const proposedStart = start.getTime();
      const proposedEnd = end.getTime();

      const hasOverlap = checkForOverlap(
        event.id,
        event.inspector,
        proposedStart,
        proposedEnd,
        prev
      );

      if (hasOverlap) {
        if (!toastShownRef.current) {
          toast({
            title: "Cannot schedule here",
            description: `Inspector ${event.inspector} already has an event scheduled during this time.`,
            variant: "destructive",
          });
          toastShownRef.current = true;
        }
        return prev;
      }

      event.start = proposedStart;
      event.end = proposedEnd;
      const updated = [...prev];
      updated[idx] = event;
      return updated;
    });
  }

  if (isLoadingInspectors || isLoadingSchedules) {
    return (
      <div className="border border-gray-200 rounded-lg overflow-hidden flex flex-col bg-[#F7F8F9] select-none shadow-sm min-h-[400px] items-center justify-center">
        <div className="text-gray-500">Loading calendar...</div>
      </div>
    );
  }

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden flex flex-col bg-[#F7F8F9] select-none shadow-sm">
      <div className="flex-1 overflow-auto scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent">
        <div className="min-w-[800px]">
          <WeekViewCalendarHeaderV2 days={days} />
          <WeekViewCalendarGridV2
            days={days}
            hours={HOURS}
            events={filteredEvents}
            handleDrop={handleDrop}
            handleDragStart={handleDragStart}
          />
        </div>
      </div>
    </div>
  );
};

export default WeekViewCalendarV2;
