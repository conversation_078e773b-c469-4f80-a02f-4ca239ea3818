
import { toast } from "sonner";

export interface ApiResponse<T = any> {
  success: boolean;
  error?: string;
  errorCode?: string;
  data: T;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    name: string;
    email: string;
    id: string;
    role: string;
  };
}

const BASE_URL = 'https://v0-building-restful-api.vercel.app';

export async function apiClient<T>(endpoint: string, options: RequestInit = {}): Promise<T | null> {
  try {
    // Add API version to the endpoint if it doesn't already include it
    let versionedEndpoint = endpoint;
    if (!endpoint.startsWith(`/api`)) {
      const endpointWithLeadingSlash = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
      versionedEndpoint = `/api${endpointWithLeadingSlash}`;
    }

    // Get the token from localStorage
    const token = localStorage.getItem('access_token');

    // Add authorization header if token exists
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      ...options.headers,
    };

    const response = await fetch(`${BASE_URL}${versionedEndpoint}`, {
      ...options,
      headers,
    });

    const result: ApiResponse<T> = await response.json();

    if (!result.success) {
      toast.error(result.error || 'An error occurred');
      return null;
    }

    return result.data;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Network error occurred';
    toast.error(errorMessage);
    return null;
  }
}
