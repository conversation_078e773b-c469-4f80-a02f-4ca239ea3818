/* This animation pops from pointer, using scale and fade */
@keyframes popupFromPointer {
  0% {
    opacity: 0;
    transform: scale(0.6);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes popupFromPointerReverse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.6);
  }
}

.animate-popupFromPointer {
  animation: popupFromPointer 0.25s cubic-bezier(0.34,1.56,0.64,1);
  will-change: transform, opacity;
}

.animate-popupFromPointer-reverse {
  animation: popupFromPointerReverse 0.25s cubic-bezier(0.34,1.56,0.64,1);
  will-change: transform, opacity;
  /* transform-origin will be overridden inline from JS */
}
