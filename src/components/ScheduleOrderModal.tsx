import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { UserCircle, Calendar, Clock } from "lucide-react";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import { inspectorsService } from "@/services/api/inspectors.service";
import { Inspector } from "@/types/inspector";

type ScheduleOrderModalProps = {
  isOpen: boolean;
  onClose: () => void;
  order?: {
    id?: string;
    orderNumber: string;
    propertyAddress: string;
    cost: number;
  };
  onSchedule?: (orderId: string, date: string, startTime: string, endTime: string, inspectorId: string) => void;
};

const ScheduleOrderModal = ({ isOpen, onClose, order, onSchedule }: ScheduleOrderModalProps) => {
  // Get tomorrow's date as the default date
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const defaultDate = format(tomorrow, 'yyyy-MM-dd');

  const [date, setDate] = useState(defaultDate);
  const [startTime, setStartTime] = useState("09:00");
  const [endTime, setEndTime] = useState("11:00");
  const [note, setNote] = useState("");
  const [selectedInspector, setSelectedInspector] = useState<string | null>(null);
  const [inspectors, setInspectors] = useState<Inspector[]>([]);
  const [isLoadingInspectors, setIsLoadingInspectors] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInspectors = async () => {
      setIsLoadingInspectors(true);
      setError(null);
      try {
        const response = await inspectorsService.getInspectors();
        if (response && response.inspectors) {
          setInspectors(response.inspectors);
        }
      } catch (err) {
        setError("Failed to load inspectors. Please try again later.");
        console.error("Error fetching inspectors:", err);
      } finally {
        setIsLoadingInspectors(false);
      }
    };

    if (isOpen) {
      fetchInspectors();
    }
  }, [isOpen]);

  // Reset form when modal is closed
  const resetForm = () => {
    setDate(defaultDate);
    setStartTime("09:00");
    setEndTime("11:00");
    setNote("");
    setSelectedInspector(null);
    setError(null);
  };

  // Handle modal close
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Validate time range
  const isValidTimeRange = () => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    return start < end;
  };

  const handleSchedule = () => {
    if (order?.id && onSchedule && selectedInspector && isValidTimeRange()) {
      onSchedule(order.id, date, startTime, endTime, selectedInspector);
    } else if (!isValidTimeRange()) {
      console.error("Invalid time range: End time must be after start time");
    } else {
      console.error("Cannot schedule: Missing order ID, inspector ID, or onSchedule callback");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Schedule Inspection Order</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <h3 className="font-semibold">Order Information</h3>
            <div className="grid gap-2 text-sm">
              <div><span className="font-medium">Order ID:</span> {order?.orderNumber}</div>
              <div><span className="font-medium">Address:</span> {order?.propertyAddress}</div>
              <div><span className="font-medium">Cost:</span> ${order?.cost}</div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Inspection Date
              </Label>
              <Input
                id="date"
                type="date"
                value={date}
                min={defaultDate}
                onChange={(e) => setDate(e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startTime" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Start Time
                </Label>
                <Input
                  id="startTime"
                  type="time"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endTime" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  End Time
                </Label>
                <Input
                  id="endTime"
                  type="time"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="note">Pre-inspection Note</Label>
            <Textarea
              id="note"
              placeholder="Add any important notes for the inspector..."
              className="min-h-[100px]"
              value={note}
              onChange={(e) => setNote(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">Available Inspectors</h3>
            <ScrollArea className="h-[200px] rounded-md border p-2">
              {error && (
                <div className="text-center p-4 text-red-600">
                  {error}
                </div>
              )}
              {isLoadingInspectors ? (
                <div className="text-center p-4 text-gray-500">
                  Loading inspectors...
                </div>
              ) : inspectors.length === 0 ? (
                <div className="text-center p-4 text-gray-500">
                  No inspectors available.
                </div>
              ) : (
                <div className="space-y-2">
                  {inspectors.map((inspector) => (
                    <div
                      key={inspector.id}
                      className={`flex items-center justify-between p-2 rounded-lg border hover:bg-gray-50 ${
                        selectedInspector === inspector.id.toString() ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <UserCircle className="h-8 w-8 text-gray-400" />
                        <div>
                          <div className="font-medium">{inspector.name}</div>
                          <div className="text-sm text-gray-500">{inspector.email}</div>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant={selectedInspector === inspector.id.toString() ? "default" : "outline"}
                        onClick={() => setSelectedInspector(inspector.id.toString())}
                      >
                        {selectedInspector === inspector.id.toString() ? "Selected" : "Select"}
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleSchedule}
            disabled={!date || !startTime || !endTime || !selectedInspector || !isValidTimeRange() || isLoadingInspectors}
          >
            Schedule Inspection
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ScheduleOrderModal;
